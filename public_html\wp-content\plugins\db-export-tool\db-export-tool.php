<?php
/**
 * Plugin Name: Database Export Tool
 * Description: 数据库导出和压缩工具
 * Version: 1.0
 * Author: Your Name
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class DatabaseExportTool {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_export_database', array($this, 'export_database'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function add_admin_menu() {
        add_management_page(
            '数据库导出工具',
            '数据库导出',
            'manage_options',
            'db-export-tool',
            array($this, 'admin_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'tools_page_db-export-tool') {
            return;
        }
        
        wp_enqueue_script('jquery');
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>数据库导出工具</h1>
            
            <div class="card">
                <h2>数据库信息</h2>
                <table class="form-table">
                    <tr>
                        <th>数据库名称</th>
                        <td><?php echo DB_NAME; ?></td>
                    </tr>
                    <tr>
                        <th>数据库主机</th>
                        <td><?php echo DB_HOST; ?></td>
                    </tr>
                    <tr>
                        <th>字符集</th>
                        <td><?php echo DB_CHARSET; ?></td>
                    </tr>
                </table>
            </div>
            
            <div class="card">
                <h2>导出操作</h2>
                <p>点击下面的按钮开始导出数据库。导出的文件将保存在 <code>wp-content/uploads/db-exports/</code> 目录中。</p>
                
                <button id="export-btn" class="button button-primary button-large">开始导出数据库</button>
                
                <div id="export-progress" style="display: none; margin-top: 20px;">
                    <div class="notice notice-info">
                        <p>正在导出数据库，请稍候...</p>
                    </div>
                </div>
                
                <div id="export-result" style="margin-top: 20px;"></div>
            </div>
            
            <div class="card">
                <h2>导出文件</h2>
                <div id="export-files">
                    <?php $this->list_export_files(); ?>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#export-btn').click(function() {
                var btn = $(this);
                btn.prop('disabled', true);
                $('#export-progress').show();
                $('#export-result').empty();
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'export_database',
                        nonce: '<?php echo wp_create_nonce('export_database'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#export-result').html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                            // 刷新文件列表
                            location.reload();
                        } else {
                            $('#export-result').html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        $('#export-result').html('<div class="notice notice-error"><p>导出失败，请重试。</p></div>');
                    },
                    complete: function() {
                        btn.prop('disabled', false);
                        $('#export-progress').hide();
                    }
                });
            });
        });
        </script>
        
        <style>
        .card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        .export-file {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .export-file:last-child {
            border-bottom: none;
        }
        .file-info {
            flex: 1;
        }
        .file-actions {
            margin-left: 20px;
        }
        </style>
        <?php
    }
    
    public function export_database() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'export_database')) {
            wp_send_json_error(array('message' => '安全验证失败'));
            return;
        }

        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => '权限不足'));
            return;
        }

        try {
            $result = $this->perform_export();
            wp_send_json_success($result);
        } catch (Exception $e) {
            // 记录详细错误到日志
            error_log('Database Export Error: ' . $e->getMessage());
            error_log('Database Export Stack Trace: ' . $e->getTraceAsString());
            wp_send_json_error(array('message' => '导出失败: ' . $e->getMessage()));
        }
    }
    
    private function perform_export() {
        // 设置执行时间和内存限制
        set_time_limit(0);
        ini_set('memory_limit', '512M');

        // 创建导出目录
        $upload_dir = wp_upload_dir();
        if (isset($upload_dir['error']) && $upload_dir['error']) {
            throw new Exception('无法获取上传目录: ' . $upload_dir['error']);
        }

        $export_dir = $upload_dir['basedir'] . '/db-exports/';

        if (!is_dir($export_dir)) {
            if (!wp_mkdir_p($export_dir)) {
                throw new Exception('无法创建导出目录: ' . $export_dir);
            }
        }

        // 检查目录权限
        if (!is_writable($export_dir)) {
            throw new Exception('导出目录不可写: ' . $export_dir);
        }

        // 生成文件名
        $filename = DB_NAME . '_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = $export_dir . $filename;

        // 连接数据库
        global $wpdb;

        // 测试数据库连接
        $test_query = $wpdb->get_var("SELECT 1");
        if ($test_query !== '1') {
            throw new Exception('数据库连接失败');
        }

        // 开始导出
        $sql_content = $this->generate_sql_dump($wpdb);

        if (empty($sql_content)) {
            throw new Exception('生成的SQL内容为空');
        }

        // 写入文件
        $bytes_written = file_put_contents($filepath, $sql_content);
        if ($bytes_written === false) {
            throw new Exception('无法写入导出文件: ' . $filepath);
        }

        if (!file_exists($filepath)) {
            throw new Exception('导出文件创建失败: ' . $filepath);
        }

        // 压缩文件
        $compressed_file = $this->compress_file($filepath);

        // 清理旧文件
        $this->clean_old_exports($export_dir, 5);

        return array(
            'message' => '数据库导出成功！文件已保存为: ' . basename($compressed_file),
            'file' => $compressed_file,
            'size' => $this->format_bytes(filesize($compressed_file))
        );
    }
    
    private function generate_sql_dump($wpdb) {
        $sql = "-- \n";
        $sql .= "-- 数据库导出文件\n";
        $sql .= "-- 数据库: " . DB_NAME . "\n";
        $sql .= "-- 导出时间: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- \n\n";

        $sql .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $sql .= "SET AUTOCOMMIT = 0;\n";
        $sql .= "START TRANSACTION;\n";
        $sql .= "SET time_zone = \"+00:00\";\n\n";

        // 获取所有表
        $tables = $wpdb->get_results("SHOW TABLES", 'ARRAY_N');

        if (empty($tables)) {
            throw new Exception('未找到任何数据表');
        }

        foreach ($tables as $table) {
            $table_name = $table[0];

            // 导出表结构
            $sql .= "-- \n";
            $sql .= "-- 表结构: `{$table_name}`\n";
            $sql .= "-- \n\n";

            $create_table = $wpdb->get_row("SHOW CREATE TABLE `{$table_name}`", 'ARRAY_N');
            if (!$create_table) {
                error_log("无法获取表结构: {$table_name}");
                continue;
            }

            $sql .= "DROP TABLE IF EXISTS `{$table_name}`;\n";
            $sql .= $create_table[1] . ";\n\n";

            // 导出表数据
            $sql .= "-- \n";
            $sql .= "-- 表数据: `{$table_name}`\n";
            $sql .= "-- \n\n";

            $rows = $wpdb->get_results("SELECT * FROM `{$table_name}`", 'ARRAY_A');

            if (!empty($rows)) {
                foreach ($rows as $row) {
                    $columns = array_keys($row);
                    $values = array_values($row);

                    // 转义值
                    $escaped_values = array_map(function($value) use ($wpdb) {
                        if ($value === null) {
                            return 'NULL';
                        }
                        return "'" . $wpdb->_escape($value) . "'";
                    }, $values);

                    $sql .= "INSERT INTO `{$table_name}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $escaped_values) . ");\n";
                }
            }

            $sql .= "\n";
        }

        $sql .= "COMMIT;\n";

        return $sql;
    }
    
    private function compress_file($filepath) {
        $compressed_file = $filepath . '.gz';
        
        $file = fopen($filepath, 'rb');
        $gz = gzopen($compressed_file, 'wb9');
        
        if ($file && $gz) {
            while (!feof($file)) {
                gzwrite($gz, fread($file, 8192));
            }
            fclose($file);
            gzclose($gz);
            
            // 删除原始文件
            unlink($filepath);
            
            return $compressed_file;
        }
        
        throw new Exception('压缩失败');
    }
    
    private function clean_old_exports($export_dir, $keep_count) {
        $files = glob($export_dir . '*.sql.gz');
        
        if (count($files) > $keep_count) {
            // 按修改时间排序
            usort($files, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
            
            // 删除多余的文件
            $files_to_delete = array_slice($files, $keep_count);
            foreach ($files_to_delete as $file) {
                unlink($file);
            }
        }
    }
    
    private function format_bytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
    
    private function list_export_files() {
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/db-exports/';
        
        if (!is_dir($export_dir)) {
            echo '<p>暂无导出文件</p>';
            return;
        }
        
        $files = glob($export_dir . '*.sql.gz');
        
        if (empty($files)) {
            echo '<p>暂无导出文件</p>';
            return;
        }
        
        // 按修改时间排序
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        foreach ($files as $file) {
            $filename = basename($file);
            $size = $this->format_bytes(filesize($file));
            $mtime = date('Y-m-d H:i:s', filemtime($file));
            $download_url = $upload_dir['baseurl'] . '/db-exports/' . $filename;
            
            echo '<div class="export-file">';
            echo '<div class="file-info">';
            echo '<strong>' . esc_html($filename) . '</strong><br>';
            echo '大小: ' . $size . ' | 创建时间: ' . $mtime;
            echo '</div>';
            echo '<div class="file-actions">';
            echo '<a href="' . esc_url($download_url) . '" class="button" download>下载</a>';
            echo '</div>';
            echo '</div>';
        }
    }
}

// 初始化插件
new DatabaseExportTool();
?>
