<?php
define( 'WP_CACHE', true ); // Added by WP Rocket

/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the web site, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * Localized language
 * * ABSPATH
 *
 * @link https://wordpress.org/support/article/editing-wp-config-php/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'db405xsom4s8ry' );

/** Database username */
define( 'DB_USER', 'uqsl0j4yc3qso' );

/** Database password */
define( 'DB_PASSWORD', 'qkyqj0ymjbyz' );

/** Database hostname */
define( 'DB_HOST', '127.0.0.1' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',          '}<wf4TM6$trl$6`5@/XR17BJ)vTy~<V<yH8mB8h/^by!m2[WC{4H2u3A1n(&tU9n' );
define( 'SECURE_AUTH_KEY',   'xhh`JZR1VB]+;V9J;}j;U}ohJ&M/G%~&|X$q{V*`hD7Plk,gpFZW9~WDNy0O85H%' );
define( 'LOGGED_IN_KEY',     '5Lk},o0o%6Q.NoiP BBn6(g2;~x7v_}TMSK`+M-.HhvH&>LSYZF[`pU=zTye6KcG' );
define( 'NONCE_KEY',         'ioa1{i!nsM%8E!D4<[a-5APaL>GF+m0,&P)28fgz)IVJ!vM%NHQ)vxm5(U)E1_vL' );
define( 'AUTH_SALT',         '1MS92OkKM5,s(%<9[@^W!]z%XE]kQub_CQ:m&(a@.yV4dW2A_IIXC$~1=yf[nibf' );
define( 'SECURE_AUTH_SALT',  'Aogv}1q0}^Q,)eO!!hZk?f3h[=N.))w)h)i4tc8../Mwjn[ $8,ZgtJJb$rEF?8%' );
define( 'LOGGED_IN_SALT',    'CURsXF8jrOw^jjHs@Wv+ipLS%Jr5O ZJlmsC9vNcRlI;SSDm(2G&CW.uiyI@<q}|' );
define( 'NONCE_SALT',        'K]M<8CBNFN6XZkw2[{-ak*/ts#?~(,QaFYHt=j|}Z@1hv[^>Sl;.lai>Q}[#Prp%' );
define( 'WP_CACHE_KEY_SALT', '*V9J(iqEIV06C5sC{gS4?XF3o~me(i7D{zEPG;9:kpb4%^r/$S(QtSmMe)mXT$r_' );


/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix = 'ciu_';


/* Add any custom values between this line and the "stop editing" line. */



/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://wordpress.org/support/article/debugging-in-wordpress/
 */
if ( ! defined( 'WP_DEBUG' ) ) {
	define( 'WP_DEBUG', false );
}

/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
@include_once('/var/lib/sec/wp-settings-pre.php'); // Added by SiteGround WordPress management system
require_once ABSPATH . 'wp-settings.php';
@include_once('/var/lib/sec/wp-settings.php'); // Added by SiteGround WordPress management system
