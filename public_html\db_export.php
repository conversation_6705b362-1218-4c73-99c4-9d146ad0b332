<?php
/**
 * 数据库导出和压缩工具
 * 用于导出 db405xsom4s8ry 数据库并压缩
 */

// 设置执行时间限制和内存限制
set_time_limit(0);
ini_set('memory_limit', '512M');

// 数据库配置（从wp-config.php获取）
define('DB_NAME', 'db405xsom4s8ry');
define('DB_USER', 'uqsl0j4yc3qso');
define('DB_PASSWORD', 'qkyqj0ymjbyz');
define('DB_HOST', '127.0.0.1');
define('DB_CHARSET', 'utf8');

class DatabaseExporter {
    private $connection;
    private $dbName;
    private $exportPath;
    
    public function __construct() {
        $this->dbName = DB_NAME;
        $this->exportPath = dirname(__FILE__) . '/exports/';
        
        // 创建导出目录
        if (!is_dir($this->exportPath)) {
            mkdir($this->exportPath, 0755, true);
        }
    }
    
    /**
     * 连接数据库
     */
    private function connect() {
        try {
            $this->connection = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASSWORD,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
            return true;
        } catch (PDOException $e) {
            echo "数据库连接失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 获取所有表名
     */
    private function getTables() {
        $stmt = $this->connection->query("SHOW TABLES");
        $tables = [];
        while ($row = $stmt->fetch()) {
            $tables[] = array_values($row)[0];
        }
        return $tables;
    }
    
    /**
     * 导出单个表的结构
     */
    private function exportTableStructure($tableName) {
        $sql = "-- \n";
        $sql .= "-- 表结构: `{$tableName}`\n";
        $sql .= "-- \n\n";
        
        // 获取创建表的SQL
        $stmt = $this->connection->query("SHOW CREATE TABLE `{$tableName}`");
        $row = $stmt->fetch();
        $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
        $sql .= $row['Create Table'] . ";\n\n";
        
        return $sql;
    }
    
    /**
     * 导出单个表的数据
     */
    private function exportTableData($tableName) {
        $sql = "-- \n";
        $sql .= "-- 表数据: `{$tableName}`\n";
        $sql .= "-- \n\n";
        
        // 获取表数据
        $stmt = $this->connection->query("SELECT * FROM `{$tableName}`");
        
        while ($row = $stmt->fetch()) {
            $columns = array_keys($row);
            $values = array_values($row);
            
            // 转义值
            $escapedValues = array_map(function($value) {
                if ($value === null) {
                    return 'NULL';
                }
                return "'" . addslashes($value) . "'";
            }, $values);
            
            $sql .= "INSERT INTO `{$tableName}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $escapedValues) . ");\n";
        }
        
        $sql .= "\n";
        return $sql;
    }
    
    /**
     * 导出完整数据库
     */
    public function exportDatabase() {
        if (!$this->connect()) {
            return false;
        }
        
        $filename = $this->dbName . '_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = $this->exportPath . $filename;
        
        echo "开始导出数据库: {$this->dbName}\n";
        echo "导出文件: {$filepath}\n";
        
        // 创建SQL文件
        $sqlContent = "-- \n";
        $sqlContent .= "-- 数据库导出文件\n";
        $sqlContent .= "-- 数据库: {$this->dbName}\n";
        $sqlContent .= "-- 导出时间: " . date('Y-m-d H:i:s') . "\n";
        $sqlContent .= "-- \n\n";
        
        $sqlContent .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $sqlContent .= "SET AUTOCOMMIT = 0;\n";
        $sqlContent .= "START TRANSACTION;\n";
        $sqlContent .= "SET time_zone = \"+00:00\";\n\n";
        
        // 获取所有表
        $tables = $this->getTables();
        echo "找到 " . count($tables) . " 个表\n";
        
        foreach ($tables as $table) {
            echo "导出表: {$table}\n";
            
            // 导出表结构
            $sqlContent .= $this->exportTableStructure($table);
            
            // 导出表数据
            $sqlContent .= $this->exportTableData($table);
        }
        
        $sqlContent .= "COMMIT;\n";
        
        // 写入文件
        if (file_put_contents($filepath, $sqlContent)) {
            echo "数据库导出成功: {$filepath}\n";
            echo "文件大小: " . $this->formatBytes(filesize($filepath)) . "\n";
            
            // 压缩文件
            return $this->compressFile($filepath);
        } else {
            echo "导出失败\n";
            return false;
        }
    }
    
    /**
     * 压缩SQL文件
     */
    private function compressFile($filepath) {
        $compressedFile = $filepath . '.gz';
        
        echo "开始压缩文件...\n";
        
        // 使用gzip压缩
        $file = fopen($filepath, 'rb');
        $gz = gzopen($compressedFile, 'wb9');
        
        if ($file && $gz) {
            while (!feof($file)) {
                gzwrite($gz, fread($file, 8192));
            }
            fclose($file);
            gzclose($gz);
            
            echo "压缩完成: {$compressedFile}\n";
            echo "压缩后大小: " . $this->formatBytes(filesize($compressedFile)) . "\n";
            echo "压缩比: " . round((1 - filesize($compressedFile) / filesize($filepath)) * 100, 2) . "%\n";
            
            // 删除原始SQL文件（可选）
            // unlink($filepath);
            
            return $compressedFile;
        } else {
            echo "压缩失败\n";
            return false;
        }
    }
    
    /**
     * 格式化文件大小
     */
    private function formatBytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 清理旧的导出文件（保留最近的N个文件）
     */
    public function cleanOldExports($keepCount = 5) {
        $files = glob($this->exportPath . $this->dbName . '_*.sql*');
        
        if (count($files) > $keepCount) {
            // 按修改时间排序
            usort($files, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
            
            // 删除多余的文件
            $filesToDelete = array_slice($files, $keepCount);
            foreach ($filesToDelete as $file) {
                unlink($file);
                echo "删除旧文件: " . basename($file) . "\n";
            }
        }
    }
}

// 使用示例
if (php_sapi_name() === 'cli' || isset($_GET['export'])) {
    $exporter = new DatabaseExporter();
    
    // 清理旧文件
    $exporter->cleanOldExports(5);
    
    // 导出数据库
    $result = $exporter->exportDatabase();
    
    if ($result) {
        echo "\n导出完成！\n";
        if (php_sapi_name() !== 'cli') {
            echo "<br><a href='exports/' target='_blank'>查看导出文件</a>";
        }
    } else {
        echo "\n导出失败！\n";
    }
} else {
    // Web界面
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>数据库导出工具</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 600px; margin: 0 auto; }
            .btn { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; }
            .btn:hover { background: #005a87; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>数据库导出工具</h1>
            <p>数据库: <strong><?php echo DB_NAME; ?></strong></p>
            <p>主机: <strong><?php echo DB_HOST; ?></strong></p>
            
            <p>
                <a href="?export=1" class="btn">开始导出数据库</a>
            </p>
            
            <p>
                <a href="exports/" target="_blank">查看导出文件</a>
            </p>
        </div>
    </body>
    </html>
    <?php
}
?>
