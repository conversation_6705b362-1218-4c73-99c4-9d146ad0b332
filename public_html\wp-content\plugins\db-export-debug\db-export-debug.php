<?php
/**
 * Plugin Name: Database Export Debug Tool
 * Description: 数据库导出调试工具 - 显示详细错误信息
 * Version: 1.0
 * Author: Debug Version
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class DatabaseExportDebug {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_debug_export_database', array($this, 'debug_export_database'));
    }
    
    public function add_admin_menu() {
        add_management_page(
            '数据库导出调试',
            '数据库导出调试',
            'manage_options',
            'db-export-debug',
            array($this, 'admin_page')
        );
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>数据库导出调试工具</h1>
            
            <div class="card">
                <h2>系统信息</h2>
                <table class="form-table">
                    <tr>
                        <th>PHP版本</th>
                        <td><?php echo PHP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th>内存限制</th>
                        <td><?php echo ini_get('memory_limit'); ?></td>
                    </tr>
                    <tr>
                        <th>执行时间限制</th>
                        <td><?php echo ini_get('max_execution_time'); ?> 秒</td>
                    </tr>
                    <tr>
                        <th>数据库名称</th>
                        <td><?php echo DB_NAME; ?></td>
                    </tr>
                    <tr>
                        <th>数据库主机</th>
                        <td><?php echo DB_HOST; ?></td>
                    </tr>
                    <tr>
                        <th>上传目录</th>
                        <td>
                            <?php 
                            $upload_dir = wp_upload_dir();
                            echo $upload_dir['basedir'];
                            if (isset($upload_dir['error']) && $upload_dir['error']) {
                                echo '<br><span style="color: red;">错误: ' . $upload_dir['error'] . '</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th>上传目录可写</th>
                        <td>
                            <?php 
                            $upload_dir = wp_upload_dir();
                            echo is_writable($upload_dir['basedir']) ? '是' : '<span style="color: red;">否</span>';
                            ?>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="card">
                <h2>数据库连接测试</h2>
                <div id="db-test-result">
                    <button id="test-db-btn" class="button">测试数据库连接</button>
                </div>
            </div>
            
            <div class="card">
                <h2>导出测试</h2>
                <button id="debug-export-btn" class="button button-primary">开始调试导出</button>
                
                <div id="debug-progress" style="display: none; margin-top: 20px;">
                    <div class="notice notice-info">
                        <p>正在执行调试导出...</p>
                    </div>
                </div>
                
                <div id="debug-result" style="margin-top: 20px;"></div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // 测试数据库连接
            $('#test-db-btn').click(function() {
                var btn = $(this);
                btn.prop('disabled', true).text('测试中...');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'debug_export_database',
                        test_type: 'db_connection'
                    },
                    success: function(response) {
                        console.log('DB Test Response:', response);
                        $('#db-test-result').html('<div class="notice notice-success"><p>数据库连接成功！</p><pre>' + JSON.stringify(response, null, 2) + '</pre></div>');
                    },
                    error: function(xhr, status, error) {
                        console.log('DB Test Error:', xhr, status, error);
                        $('#db-test-result').html('<div class="notice notice-error"><p>数据库连接失败</p><pre>状态: ' + xhr.status + '\n错误: ' + error + '\n响应: ' + xhr.responseText + '</pre></div>');
                    },
                    complete: function() {
                        btn.prop('disabled', false).text('测试数据库连接');
                    }
                });
            });
            
            // 调试导出
            $('#debug-export-btn').click(function() {
                var btn = $(this);
                btn.prop('disabled', true);
                $('#debug-progress').show();
                $('#debug-result').empty();
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'debug_export_database',
                        test_type: 'full_export'
                    },
                    success: function(response) {
                        console.log('Export Response:', response);
                        
                        if (response && response.success) {
                            $('#debug-result').html('<div class="notice notice-success"><p>导出成功！</p><pre>' + JSON.stringify(response, null, 2) + '</pre></div>');
                        } else {
                            $('#debug-result').html('<div class="notice notice-error"><p>导出失败</p><pre>' + JSON.stringify(response, null, 2) + '</pre></div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('Export Error:', xhr, status, error);
                        
                        var debugInfo = '状态码: ' + xhr.status + '\n';
                        debugInfo += '错误类型: ' + status + '\n';
                        debugInfo += '错误信息: ' + error + '\n';
                        debugInfo += '完整响应:\n' + xhr.responseText;
                        
                        $('#debug-result').html('<div class="notice notice-error"><p>AJAX请求失败</p><pre>' + debugInfo + '</pre></div>');
                    },
                    complete: function() {
                        btn.prop('disabled', false);
                        $('#debug-progress').hide();
                    }
                });
            });
        });
        </script>
        
        <style>
        .card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        pre {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        </style>
        <?php
    }
    
    public function debug_export_database() {
        // 开启错误显示
        ini_set('display_errors', 1);
        error_reporting(E_ALL);
        
        // 设置JSON响应头
        header('Content-Type: application/json');
        
        try {
            $test_type = isset($_POST['test_type']) ? $_POST['test_type'] : 'db_connection';
            
            if ($test_type === 'db_connection') {
                // 只测试数据库连接
                global $wpdb;
                
                $result = $wpdb->get_var("SELECT 1");
                
                if ($result === '1') {
                    echo json_encode(array(
                        'success' => true,
                        'data' => array(
                            'message' => '数据库连接成功',
                            'db_version' => $wpdb->get_var("SELECT VERSION()"),
                            'table_count' => $wpdb->get_var("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '" . DB_NAME . "'")
                        )
                    ));
                } else {
                    throw new Exception('数据库查询失败');
                }
                
            } else if ($test_type === 'full_export') {
                // 执行完整导出测试
                $this->perform_debug_export();
            }
            
        } catch (Exception $e) {
            echo json_encode(array(
                'success' => false,
                'data' => array(
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        } catch (Error $e) {
            echo json_encode(array(
                'success' => false,
                'data' => array(
                    'message' => 'PHP Fatal Error: ' . $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
        
        wp_die();
    }
    
    private function perform_debug_export() {
        // 设置执行时间和内存限制
        set_time_limit(0);
        ini_set('memory_limit', '512M');
        
        $steps = array();
        
        // 步骤1: 检查上传目录
        $upload_dir = wp_upload_dir();
        if (isset($upload_dir['error']) && $upload_dir['error']) {
            throw new Exception('上传目录错误: ' . $upload_dir['error']);
        }
        $steps[] = '上传目录检查通过: ' . $upload_dir['basedir'];
        
        // 步骤2: 创建导出目录
        $export_dir = $upload_dir['basedir'] . '/db-exports/';
        if (!is_dir($export_dir)) {
            if (!wp_mkdir_p($export_dir)) {
                throw new Exception('无法创建导出目录: ' . $export_dir);
            }
        }
        $steps[] = '导出目录创建成功: ' . $export_dir;
        
        // 步骤3: 检查目录权限
        if (!is_writable($export_dir)) {
            throw new Exception('导出目录不可写: ' . $export_dir);
        }
        $steps[] = '目录权限检查通过';
        
        // 步骤4: 测试数据库连接
        global $wpdb;
        $test_query = $wpdb->get_var("SELECT 1");
        if ($test_query !== '1') {
            throw new Exception('数据库连接测试失败');
        }
        $steps[] = '数据库连接测试通过';
        
        // 步骤5: 获取表列表
        $tables = $wpdb->get_results("SHOW TABLES", ARRAY_N);
        if (empty($tables)) {
            throw new Exception('未找到任何数据表');
        }
        $steps[] = '找到 ' . count($tables) . ' 个数据表';
        
        // 步骤6: 生成文件名
        $filename = DB_NAME . '_debug_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = $export_dir . $filename;
        $steps[] = '生成文件名: ' . $filename;
        
        // 步骤7: 创建简单的SQL内容
        $sql_content = "-- Debug Export Test\n";
        $sql_content .= "-- Database: " . DB_NAME . "\n";
        $sql_content .= "-- Time: " . date('Y-m-d H:i:s') . "\n";
        $sql_content .= "-- Tables found: " . count($tables) . "\n\n";
        
        foreach ($tables as $table) {
            $table_name = $table[0];
            $sql_content .= "-- Table: {$table_name}\n";
        }
        
        // 步骤8: 写入文件
        $bytes_written = file_put_contents($filepath, $sql_content);
        if ($bytes_written === false) {
            throw new Exception('无法写入文件: ' . $filepath);
        }
        $steps[] = '文件写入成功，写入 ' . $bytes_written . ' 字节';
        
        // 步骤9: 验证文件
        if (!file_exists($filepath)) {
            throw new Exception('文件创建验证失败');
        }
        $file_size = filesize($filepath);
        $steps[] = '文件验证成功，大小: ' . $file_size . ' 字节';
        
        echo json_encode(array(
            'success' => true,
            'data' => array(
                'message' => '调试导出完成',
                'steps' => $steps,
                'file' => $filename,
                'path' => $filepath,
                'size' => $file_size
            )
        ));
    }
}

// 初始化插件
new DatabaseExportDebug();
?>
