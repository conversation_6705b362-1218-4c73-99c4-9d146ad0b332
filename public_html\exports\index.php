<?php
/**
 * 导出文件浏览器
 */

$exportDir = __DIR__;
$files = glob($exportDir . '/*.{sql,gz}', GLOB_BRACE);

// 按修改时间排序（最新的在前）
usort($files, function($a, $b) {
    return filemtime($b) - filemtime($a);
});

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

// 处理文件下载
if (isset($_GET['download']) && $_GET['download']) {
    $file = $exportDir . '/' . basename($_GET['download']);
    if (file_exists($file)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));
        readfile($file);
        exit;
    }
}

// 处理文件删除
if (isset($_GET['delete']) && $_GET['delete']) {
    $file = $exportDir . '/' . basename($_GET['delete']);
    if (file_exists($file)) {
        unlink($file);
        header('Location: index.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>数据库导出文件</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px;
        }
        th, td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid #ddd;
        }
        th { 
            background-color: #f8f9fa; 
            font-weight: bold;
        }
        tr:hover { 
            background-color: #f8f9fa; 
        }
        .btn { 
            background: #007cba; 
            color: white; 
            padding: 6px 12px; 
            text-decoration: none; 
            border-radius: 3px; 
            font-size: 12px;
            margin-right: 5px;
        }
        .btn:hover { 
            background: #005a87; 
        }
        .btn-danger { 
            background: #dc3545; 
        }
        .btn-danger:hover { 
            background: #c82333; 
        }
        .empty { 
            text-align: center; 
            color: #666; 
            padding: 40px;
        }
        .header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            margin-bottom: 20px;
        }
        .file-type { 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-size: 11px; 
            font-weight: bold;
        }
        .sql { 
            background: #e3f2fd; 
            color: #1976d2; 
        }
        .gz { 
            background: #f3e5f5; 
            color: #7b1fa2; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数据库导出文件</h1>
            <a href="../db_export.php" class="btn">返回导出工具</a>
        </div>
        
        <?php if (empty($files)): ?>
            <div class="empty">
                <p>暂无导出文件</p>
                <a href="../db_export.php?export=1" class="btn">立即导出数据库</a>
            </div>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>类型</th>
                        <th>大小</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($files as $file): ?>
                        <?php 
                        $filename = basename($file);
                        $extension = pathinfo($file, PATHINFO_EXTENSION);
                        $size = filesize($file);
                        $mtime = filemtime($file);
                        ?>
                        <tr>
                            <td><?php echo htmlspecialchars($filename); ?></td>
                            <td>
                                <span class="file-type <?php echo $extension; ?>">
                                    <?php echo strtoupper($extension); ?>
                                </span>
                            </td>
                            <td><?php echo formatBytes($size); ?></td>
                            <td><?php echo date('Y-m-d H:i:s', $mtime); ?></td>
                            <td>
                                <a href="?download=<?php echo urlencode($filename); ?>" class="btn">下载</a>
                                <a href="?delete=<?php echo urlencode($filename); ?>" 
                                   class="btn btn-danger" 
                                   onclick="return confirm('确定要删除这个文件吗？')">删除</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 3px;">
                <strong>说明：</strong>
                <ul style="margin: 10px 0;">
                    <li><strong>SQL文件</strong>：未压缩的数据库导出文件</li>
                    <li><strong>GZ文件</strong>：压缩后的数据库导出文件（推荐下载）</li>
                    <li>系统会自动保留最近的5个导出文件</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
