/*------------------------------------------------------------------------------
  27.0 - Localization
------------------------------------------------------------------------------*/

/* RTL except Hebrew (see below): Tahoma as the first font; */
body.rtl,
body.rtl .press-this a.wp-switch-editor {
	font-family: Tahoma, Arial, sans-serif;
}

/* Arial is best for RTL headings. */
.rtl h1,
.rtl h2,
.rtl h3,
.rtl h4,
.rtl h5,
.rtl h6 {
	font-family: Arial, sans-serif;
	font-weight: 600;
}

/* he_IL: Remove <PERSON> from the font stack. Arial is best for Hebrew. */
body.locale-he-il,
body.locale-he-il .press-this a.wp-switch-editor {
	font-family: Arial, sans-serif;
}

/* he_IL: Have <em> be bold rather than italic. */
.locale-he-il em {
	font-style: normal;
	font-weight: 600;
}

/* zh_CN: Remove italic properties. */
.locale-zh-cn .howto,
.locale-zh-cn .tablenav .displaying-num,
.locale-zh-cn .js .input-with-default-title,
.locale-zh-cn .link-to-original,
.locale-zh-cn .inline-edit-row fieldset span.title,
.locale-zh-cn .inline-edit-row fieldset span.checkbox-title,
.locale-zh-cn #utc-time,
.locale-zh-cn #local-time,
.locale-zh-cn p.install-help,
.locale-zh-cn p.help,
.locale-zh-cn p.description,
.locale-zh-cn span.description,
.locale-zh-cn .form-wrap p {
	font-style: normal;
}

/* zh_CN: Enlarge dashboard widget 'Configure' link */
.locale-zh-cn .hdnle a { font-size: 12px; }

/* zn_CH: Enlarge font size, set font-size: normal */
.locale-zh-cn form.upgrade .hint { font-style: normal; font-size: 100%; }

/* zh_CN: Enlarge font-size. */
.locale-zh-cn #sort-buttons { font-size: 1em !important; }

/* de_DE: Text needs more space for translation */
.locale-de-de #customize-header-actions .button,
.locale-de-de-formal #customize-header-actions .button {
	padding: 0 5px 1px; /* default 0 10px 1px */
}
.locale-de-de #customize-header-actions .spinner,
.locale-de-de-formal #customize-header-actions .spinner {
	margin: 16px 3px 0; /* default 16px 4px 0 5px */
}
body[class*="locale-de-"] .inline-edit-row fieldset label span.title,
body[class*="locale-de-"] .inline-edit-row fieldset.inline-edit-date legend {
	width: 7em; /* default 6em */
}
body[class*="locale-de-"] .inline-edit-row fieldset label span.input-text-wrap,
body[class*="locale-de-"] .inline-edit-row fieldset .timestamp-wrap {
	margin-left: 7em; /* default 6em */
}

/* ru_RU: Text needs more room to breathe. */
.locale-ru-ru #adminmenu {
	width: inherit; /* back-compat for pre-3.2 */
}
.locale-ru-ru #adminmenu,
.locale-ru-ru #wpbody {
	margin-left: 0; /* back-compat for pre-3.2 */
}
.locale-ru-ru .inline-edit-row fieldset label span.title,
.locale-ru-ru .inline-edit-row fieldset.inline-edit-date legend {
	width: 8em; /* default 6em */
}
.locale-ru-ru .inline-edit-row fieldset label span.input-text-wrap,
.locale-ru-ru .inline-edit-row fieldset .timestamp-wrap {
	margin-left: 8em; /* default 6em */
}
.locale-ru-ru.post-php .tagsdiv .newtag,
.locale-ru-ru.post-new-php .tagsdiv .newtag {
	width: 165px; /* default 180px - 15px */
}
.locale-ru-ru.press-this .posting {
	margin-right: 277px; /* default 252px + 25px */
}
.locale-ru-ru .press-this-sidebar {
	width: 265px; /* default 240px + 25px */
}
.locale-ru-ru #customize-header-actions .button {
	padding: 0 5px 1px; /* default 0 10px 1px */
}
.locale-ru-ru #customize-header-actions .spinner {
	margin: 16px 3px 0; /* default 16px 4px 0 5px */
}

/* lt_LT: QuickEdit */
.locale-lt-lt .inline-edit-row fieldset label span.title,
.locale-lt-lt .inline-edit-row fieldset.inline-edit-date legend {
	width: 8em; /* default 6em */
}
.locale-lt-lt .inline-edit-row fieldset label span.input-text-wrap,
.locale-lt-lt .inline-edit-row fieldset .timestamp-wrap {
	margin-left: 8em; /* default 6em */
}

/* Fix overridden width for adjusted locales */
body[class*="locale-de-"] .quick-edit-row-post fieldset.inline-edit-col-right label span.title,
.locale-ru-ru .quick-edit-row-post fieldset.inline-edit-col-right label span.title,
.locale-lt-lt .quick-edit-row-post fieldset.inline-edit-col-right label span.title {
	width: auto;
}

@media screen and (max-width: 782px) {
	body[class*="locale-de-"] .inline-edit-row fieldset label span.input-text-wrap,
	body[class*="locale-de-"] .inline-edit-row fieldset .timestamp-wrap,
	.locale-ru-ru .inline-edit-row fieldset label span.input-text-wrap,
	.locale-ru-ru .inline-edit-row fieldset .timestamp-wrap,
	.locale-lt-lt .inline-edit-row fieldset label span.input-text-wrap,
	.locale-lt-lt .inline-edit-row fieldset .timestamp-wrap {
		margin-left: 0;
	}
}
