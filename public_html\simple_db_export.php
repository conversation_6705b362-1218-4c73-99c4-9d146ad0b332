<?php
/**
 * 简单数据库导出工具
 * 直接访问版本，不依赖WordPress
 */

// 设置执行时间和内存限制
set_time_limit(0);
ini_set('memory_limit', '512M');

// 数据库配置
define('DB_NAME', 'db405xsom4s8ry');
define('DB_USER', 'uqsl0j4yc3qso');
define('DB_PASSWORD', 'qkyqj0ymjbyz');
define('DB_HOST', '127.0.0.1');
define('DB_CHARSET', 'utf8');

class SimpleDBExporter {
    private $connection;
    private $exportPath;
    
    public function __construct() {
        $this->exportPath = dirname(__FILE__) . '/db-exports/';
        
        // 创建导出目录
        if (!is_dir($this->exportPath)) {
            mkdir($this->exportPath, 0755, true);
        }
    }
    
    /**
     * 连接数据库
     */
    private function connect() {
        try {
            $this->connection = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASSWORD,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
            return true;
        } catch (PDOException $e) {
            $this->logError("数据库连接失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 使用mysqldump命令导出数据库
     */
    public function exportWithMysqldump() {
        $filename = DB_NAME . '_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = $this->exportPath . $filename;
        
        // 构建mysqldump命令
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s 2>&1',
            escapeshellarg(DB_HOST),
            escapeshellarg(DB_USER),
            escapeshellarg(DB_PASSWORD),
            escapeshellarg(DB_NAME),
            escapeshellarg($filepath)
        );
        
        $this->logMessage("开始使用mysqldump导出数据库...");
        $this->logMessage("命令: mysqldump --host=" . DB_HOST . " --user=" . DB_USER . " --password=*** " . DB_NAME);
        
        // 执行命令
        $output = [];
        $return_code = 0;
        exec($command, $output, $return_code);
        
        if ($return_code === 0 && file_exists($filepath) && filesize($filepath) > 0) {
            $this->logMessage("mysqldump导出成功: " . $filepath);
            $this->logMessage("文件大小: " . $this->formatBytes(filesize($filepath)));
            
            // 压缩文件
            $compressed_file = $this->compressFile($filepath);
            
            // 清理旧文件
            $this->cleanOldExports(5);
            
            return $compressed_file;
        } else {
            $this->logError("mysqldump导出失败，返回码: " . $return_code);
            if (!empty($output)) {
                $this->logError("错误输出: " . implode("\n", $output));
            }
            
            // 尝试PHP方式导出
            return $this->exportWithPHP();
        }
    }
    
    /**
     * 使用PHP方式导出数据库
     */
    public function exportWithPHP() {
        if (!$this->connect()) {
            return false;
        }
        
        $filename = DB_NAME . '_' . date('Y-m-d_H-i-s') . '_php.sql';
        $filepath = $this->exportPath . $filename;
        
        $this->logMessage("开始使用PHP方式导出数据库...");
        
        try {
            $sqlContent = $this->generateSQLDump();
            
            if (file_put_contents($filepath, $sqlContent)) {
                $this->logMessage("PHP导出成功: " . $filepath);
                $this->logMessage("文件大小: " . $this->formatBytes(filesize($filepath)));
                
                // 压缩文件
                $compressed_file = $this->compressFile($filepath);
                
                // 清理旧文件
                $this->cleanOldExports(5);
                
                return $compressed_file;
            } else {
                $this->logError("无法写入导出文件: " . $filepath);
                return false;
            }
        } catch (Exception $e) {
            $this->logError("PHP导出失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成SQL转储
     */
    private function generateSQLDump() {
        $sql = "-- \n";
        $sql .= "-- 数据库导出文件\n";
        $sql .= "-- 数据库: " . DB_NAME . "\n";
        $sql .= "-- 导出时间: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- 导出方式: PHP PDO\n";
        $sql .= "-- \n\n";
        
        $sql .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $sql .= "SET AUTOCOMMIT = 0;\n";
        $sql .= "START TRANSACTION;\n";
        $sql .= "SET time_zone = \"+00:00\";\n\n";
        
        // 获取所有表
        $stmt = $this->connection->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $this->logMessage("找到 " . count($tables) . " 个表");
        
        foreach ($tables as $tableName) {
            $this->logMessage("导出表: " . $tableName);
            
            // 导出表结构
            $sql .= $this->exportTableStructure($tableName);
            
            // 导出表数据
            $sql .= $this->exportTableData($tableName);
        }
        
        $sql .= "COMMIT;\n";
        
        return $sql;
    }
    
    /**
     * 导出表结构
     */
    private function exportTableStructure($tableName) {
        $sql = "-- \n";
        $sql .= "-- 表结构: `{$tableName}`\n";
        $sql .= "-- \n\n";
        
        $stmt = $this->connection->query("SHOW CREATE TABLE `{$tableName}`");
        $row = $stmt->fetch(PDO::FETCH_NUM);
        
        $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
        $sql .= $row[1] . ";\n\n";
        
        return $sql;
    }
    
    /**
     * 导出表数据
     */
    private function exportTableData($tableName) {
        $sql = "-- \n";
        $sql .= "-- 表数据: `{$tableName}`\n";
        $sql .= "-- \n\n";
        
        $stmt = $this->connection->query("SELECT * FROM `{$tableName}`");
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns = array_keys($row);
            $values = array_values($row);
            
            // 转义值
            $escapedValues = array_map(function($value) {
                if ($value === null) {
                    return 'NULL';
                }
                return "'" . addslashes($value) . "'";
            }, $values);
            
            $sql .= "INSERT INTO `{$tableName}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $escapedValues) . ");\n";
        }
        
        $sql .= "\n";
        return $sql;
    }
    
    /**
     * 压缩文件
     */
    private function compressFile($filepath) {
        $compressedFile = $filepath . '.gz';
        
        $this->logMessage("开始压缩文件...");
        
        $file = fopen($filepath, 'rb');
        $gz = gzopen($compressedFile, 'wb9');
        
        if ($file && $gz) {
            while (!feof($file)) {
                gzwrite($gz, fread($file, 8192));
            }
            fclose($file);
            gzclose($gz);
            
            $this->logMessage("压缩完成: " . basename($compressedFile));
            $this->logMessage("压缩后大小: " . $this->formatBytes(filesize($compressedFile)));
            $this->logMessage("压缩比: " . round((1 - filesize($compressedFile) / filesize($filepath)) * 100, 2) . "%");
            
            // 删除原始文件
            unlink($filepath);
            
            return $compressedFile;
        } else {
            $this->logError("压缩失败");
            return $filepath;
        }
    }
    
    /**
     * 清理旧文件
     */
    private function cleanOldExports($keepCount = 5) {
        $files = glob($this->exportPath . DB_NAME . '_*.sql*');
        
        if (count($files) > $keepCount) {
            usort($files, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
            
            $filesToDelete = array_slice($files, $keepCount);
            foreach ($filesToDelete as $file) {
                unlink($file);
                $this->logMessage("删除旧文件: " . basename($file));
            }
        }
    }
    
    /**
     * 格式化文件大小
     */
    private function formatBytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 记录消息
     */
    private function logMessage($message) {
        $timestamp = date('Y-m-d H:i:s');
        echo "[{$timestamp}] {$message}\n";
        if (php_sapi_name() !== 'cli') {
            echo "<br>";
        }
        flush();
    }
    
    /**
     * 记录错误
     */
    private function logError($message) {
        $timestamp = date('Y-m-d H:i:s');
        echo "[{$timestamp}] ERROR: {$message}\n";
        if (php_sapi_name() !== 'cli') {
            echo "<br>";
        }
        flush();
        error_log("DB Export Error: " . $message);
    }
    
    /**
     * 列出导出文件
     */
    public function listExportFiles() {
        $files = glob($this->exportPath . '*.{sql,gz}', GLOB_BRACE);
        
        if (empty($files)) {
            return [];
        }
        
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        $fileList = [];
        foreach ($files as $file) {
            $fileList[] = [
                'name' => basename($file),
                'path' => $file,
                'size' => filesize($file),
                'size_formatted' => $this->formatBytes(filesize($file)),
                'mtime' => filemtime($file),
                'mtime_formatted' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }
        
        return $fileList;
    }
}

// 处理请求
if (isset($_GET['action'])) {
    $exporter = new SimpleDBExporter();
    
    switch ($_GET['action']) {
        case 'export':
            echo "<h2>数据库导出</h2>";
            echo "<pre>";
            
            $result = $exporter->exportWithMysqldump();
            
            if ($result) {
                echo "\n导出完成！文件: " . basename($result) . "\n";
                echo '<a href="?action=list">查看所有导出文件</a>';
            } else {
                echo "\n导出失败！\n";
            }
            echo "</pre>";
            break;
            
        case 'list':
            $files = $exporter->listExportFiles();
            echo "<h2>导出文件列表</h2>";
            
            if (empty($files)) {
                echo "<p>暂无导出文件</p>";
            } else {
                echo "<table border='1' cellpadding='10'>";
                echo "<tr><th>文件名</th><th>大小</th><th>创建时间</th><th>操作</th></tr>";
                
                foreach ($files as $file) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($file['name']) . "</td>";
                    echo "<td>" . $file['size_formatted'] . "</td>";
                    echo "<td>" . $file['mtime_formatted'] . "</td>";
                    echo "<td><a href='db-exports/" . urlencode($file['name']) . "' download>下载</a></td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            }
            
            echo '<p><a href="?action=export">导出新的备份</a> | <a href="?">返回首页</a></p>';
            break;
            
        default:
            echo "<h2>未知操作</h2>";
            break;
    }
} else {
    // 显示主页面
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>简单数据库导出工具</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 600px; margin: 0 auto; }
            .btn { 
                background: #007cba; 
                color: white; 
                padding: 10px 20px; 
                text-decoration: none; 
                border-radius: 3px; 
                display: inline-block;
                margin: 10px 5px;
            }
            .btn:hover { background: #005a87; }
            .info { background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>简单数据库导出工具</h1>
            
            <div class="info">
                <h3>数据库信息</h3>
                <p><strong>数据库名:</strong> <?php echo DB_NAME; ?></p>
                <p><strong>主机:</strong> <?php echo DB_HOST; ?></p>
                <p><strong>用户:</strong> <?php echo DB_USER; ?></p>
            </div>
            
            <div>
                <a href="?action=export" class="btn">开始导出数据库</a>
                <a href="?action=list" class="btn">查看导出文件</a>
            </div>
            
            <div class="info">
                <h3>说明</h3>
                <ul>
                    <li>优先使用 mysqldump 命令导出（更快更可靠）</li>
                    <li>如果 mysqldump 不可用，自动切换到 PHP 方式</li>
                    <li>导出的文件会自动压缩为 .gz 格式</li>
                    <li>自动保留最近的 5 个导出文件</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
